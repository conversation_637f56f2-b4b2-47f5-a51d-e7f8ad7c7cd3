import requests
import random
import google.generativeai as genai
import time
import os
from moviepy.editor import VideoFileClip, CompositeVideoClip, AudioFileClip
from moviepy.config import change_settings
import tempfile
from PIL import Image, ImageDraw, ImageFont
import numpy as np

# ========== CONFIG ==========
ACCESS_TOKEN = "EAAVh81wZCRlEBPYHKPaC34VnBWWRbGWzAjaSWDDwvgciTZClG2TJkl8dFihV7AgwJZCYrh2l0Nzc70iEF8GHZC26vrvPKczdErxOcd7e0gdP4wzgAXi6fHm8MkULj22UiKszS8iwk8PK3DiXQ99tRI2dDkSZBuXwd9OHdJqlmm0ZCU6Qn1hZANNXQxsnrVXnKMbNRLPSbQmdhAy"
INSTAGRAM_ACCOUNT_ID = "*****************"
PEXELS_API_KEY = "Y42fCOJyjmNn88IP6nGhBmidAX2J8EiyMpkk7vse4IjB3YsILyAoBEhw"
PIXABAY_API_KEY = "**********************************"
FREESOUND_API_KEY = "zQWBy01ioXG1wWTIHQNtf3mCOxi3Hmrkka0LK0ab"

# TODO: Replace with your valid Gemini API key
GEMINI_API_KEY = "AIzaSyAkelKzPAm0x36YBoMhqgWGt0lPWOYxwDk"
genai.configure(api_key=GEMINI_API_KEY)

# Initialize the model
model = genai.GenerativeModel('gemini-1.5-flash')

# ================= MEDIA FETCH =================
def fetch_images(per_page=10):
    url = f"https://api.pexels.com/v1/search?query=motivational&per_page={per_page}"
    headers = {"Authorization": PEXELS_API_KEY}
    response = requests.get(url, headers=headers)
    data = response.json()
    return [photo['src']['original'] for photo in data.get("photos", [])]

def fetch_videos(per_page=5):
    url = f"https://api.pexels.com/videos/search?query=motivational&per_page={per_page}"
    headers = {"Authorization": PEXELS_API_KEY}
    response = requests.get(url, headers=headers)
    data = response.json()
    return [vid['video_files'][0]['link'] for vid in data.get("videos", [])]

# ================= AUDIO FETCH =================
def fetch_background_music():
    """
    Fetch royalty-free music from Pixabay API
    """
    try:
        # Note: Pixabay's music API might have different endpoint or requirements
        # Let's try the general search API with audio filter
        url = "https://pixabay.com/api/"
        params = {
            "key": PIXABAY_API_KEY,
            "q": "motivational background music",
            "category": "music",
            "per_page": 10,
            "safesearch": "true",
            "audio_type": "music"
        }
        
        response = requests.get(url, params=params)
        if response.status_code == 200:
            data = response.json()
            if "hits" in data and data["hits"]:
                # Return download URLs for the music tracks
                return [track.get("download") for track in data["hits"] if track.get("download")]
        
        print("⚠️ No music found from Pixabay API, trying Freesound...")
        return []
            
    except Exception as e:
        print(f"⚠️ Error fetching music from Pixabay: {e}")
        return []

def fetch_freesound_music():
    """
    Alternative: Fetch music from Freesound API
    """
    try:
        url = "https://freesound.org/apiv2/search/text/"
        headers = {"Authorization": f"Token {FREESOUND_API_KEY}"}
        params = {
            "query": "motivational music",
            "filter": "duration:[10.0 TO 300.0] type:mp3",  # 10 seconds to 5 minutes, MP3 only
            "sort": "downloads_desc",
            "page_size": 10
        }
        
        response = requests.get(url, headers=headers, params=params)
        data = response.json()
        
        if "results" in data:
            # Get high-quality preview URLs (these are free to use)
            return [sound["previews"]["preview-hq-mp3"] for sound in data["results"] 
                   if "previews" in sound and "preview-hq-mp3" in sound["previews"]]
        else:
            return []
            
    except Exception as e:
        print(f"⚠️ Error fetching from Freesound: {e}")
        return []

def get_music_tracks():
    """
    Get music from available sources with fallback options
    """
    # Try Pixabay first
    music_urls = fetch_background_music()
    
    # If Pixabay fails, try Freesound
    if not music_urls:
        music_urls = fetch_freesound_music()
    
    # If both APIs fail, use local files as fallback
    if not music_urls:
        music_folder = "music"
        if os.path.exists(music_folder):
            music_files = [os.path.join(music_folder, f) for f in os.listdir(music_folder) 
                          if f.endswith(('.mp3', '.wav', '.m4a'))]
            return music_files
    
    return music_urls

# ================= VIDEO PROCESSING =================
def add_music_and_subtitles(video_url, caption_text, output_path):
    """
    Download video, add background music and subtitles, then save locally
    """
    try:
        # Download the video
        print("📥 Downloading video...")
        video_response = requests.get(video_url)
        
        with tempfile.NamedTemporaryFile(delete=False, suffix='.mp4') as temp_video:
            temp_video.write(video_response.content)
            temp_video_path = temp_video.name
        
        # Load video
        video = VideoFileClip(temp_video_path)
        
        # Get background music
        music_files = get_music_tracks()
        final_video = video
        
        if music_files:
            print("🎵 Adding background music...")
            music_path = random.choice(music_files)
            
            # Load and adjust music
            if music_path.startswith('http'):
                # Download music if it's a URL
                music_response = requests.get(music_path)
                with tempfile.NamedTemporaryFile(delete=False, suffix='.mp3') as temp_music:
                    temp_music.write(music_response.content)
                    music_path = temp_music.name
            
            audio = AudioFileClip(music_path)
            
            # Adjust audio length to match video and reduce volume
            audio = audio.subclip(0, min(audio.duration, video.duration))
            audio = audio.volumex(0.3)  # Reduce volume to 30%
            
            # Combine original audio (if any) with background music
            if video.audio:
                final_audio = video.audio.volumex(0.7)  # Reduce original audio to 70%
                combined_audio = CompositeAudioClip([final_audio, audio])
                final_video = video.set_audio(combined_audio)
            else:
                final_video = video.set_audio(audio)

        # Write the final video to the output path
        print("💾 Saving processed video...")
        final_video.write_videofile(
            output_path,
            codec='libx264',
            audio_codec='aac',
            temp_audiofile='temp-audio.m4a',
            remove_temp=True,
            verbose=False,
            logger=None
        )

        # Cleanup
        final_video.close()
        video.close()
        if music_files and 'audio' in locals():
            audio.close()
        os.unlink(temp_video_path)

        return True

    except Exception as e:
        print(f"❌ Error adding music and subtitles: {e}")
        return False

def create_subtitle_overlay(text, video_size, duration):
    """
    Create subtitle overlay using PIL instead of TextClip to avoid ImageMagick
    """
    try:
        width, height = video_size
        
        # Create a transparent image for subtitle
        img = Image.new('RGBA', (width, height), (0, 0, 0, 0))
        draw = ImageDraw.Draw(img)
        
        # Try to use a default font, fallback to default if not available
        try:
            font = ImageFont.truetype("/usr/share/fonts/truetype/dejavu/DejaVuSans-Bold.ttf", 28)
        except:
            font = ImageFont.load_default()
        
        # Wrap text
        words = text.split()
        if len(words) > 8:
            text = ' '.join(words[:8]) + '...'
        
        # Get text size
        bbox = draw.textbbox((0, 0), text, font=font)
        text_width = bbox[2] - bbox[0]
        text_height = bbox[3] - bbox[1]
        
        # Position text at bottom center
        x = (width - text_width) // 2
        y = height - text_height - 50
        
        # Draw text with outline
        outline_range = 2
        for adj_x in range(-outline_range, outline_range + 1):
            for adj_y in range(-outline_range, outline_range + 1):
                draw.text((x + adj_x, y + adj_y), text, font=font, fill='black')
        
        # Draw main text
        draw.text((x, y), text, font=font, fill='white')
        
        # Convert PIL image to numpy array for MoviePy
        img_array = np.array(img)
        
        # Create VideoClip from image array
        from moviepy.editor import ImageClip
        subtitle_clip = ImageClip(img_array, duration=duration, transparent=True)
        
        return subtitle_clip
        
    except Exception as e:
        print(f"⚠️ Error creating subtitle overlay: {e}")
        return None
        
        # Write the final video
        print("💾 Saving processed video...")
        final_video.write_videofile(
            output_path,
            codec='libx264',
            audio_codec='aac',
            temp_audiofile='temp-audio.m4a',
            remove_temp=True,
            verbose=False,
            logger=None
        )
        
        # Cleanup
        final_video.close()
        video.close()
        if music_files and 'audio' in locals():
            audio.close()
        os.unlink(temp_video_path)
        
        return True
        
    except Exception as e:
        print(f"❌ Error processing video: {e}")
        return False
def generate_caption(post_type="image"):
    prompt = (
        f"Write a short Instagram caption for a {post_type} post in the "
        "personal development/motivation niche, 100-150 words, with hashtags, "
        "and a call-to-action question at the end."
    )
    try:
        response = model.generate_content(prompt)
        return response.text
    except Exception as e:
        print(f"❌ Error generating caption: {e}")
        # Fallback caption
        return f"Stay motivated and keep pushing forward! 💪 #motivation #mindset #success #growth #inspiration What's your biggest goal this week?"

# ================= CREATE & PUBLISH MEDIA =================
def create_media(payload):
    url = f"https://graph.facebook.com/v21.0/{INSTAGRAM_ACCOUNT_ID}/media"
    response = requests.post(url, data=payload)
    return response.json()

def publish_media(container_id, max_retries=5, wait_time=30):  # Using your 30 seconds
    url = f"https://graph.facebook.com/v21.0/{INSTAGRAM_ACCOUNT_ID}/media_publish"
    payload = {"creation_id": container_id, "access_token": ACCESS_TOKEN}
    
    for attempt in range(max_retries):
        response = requests.post(url, data=payload)
        result = response.json()
        
        # Check if media is still processing
        if "error" in result and result["error"].get("code") == 9007:
            print(f"⏳ Media still processing, waiting {wait_time} seconds... (attempt {attempt + 1}/{max_retries})")
            time.sleep(wait_time)
            continue
        else:
            return result
    
    # If all retries failed
    return {"error": {"message": "Media failed to process after maximum retries"}}

def check_media_status(container_id):
    """Check if media container is ready for publishing"""
    url = f"https://graph.facebook.com/v21.0/{container_id}"
    params = {"fields": "status_code", "access_token": ACCESS_TOKEN}
    response = requests.get(url, params=params)
    return response.json()

# ================= POST TYPES =================
def post_image():
    images = fetch_images()
    if not images: 
        print("❌ No images found")
        return
    image_url = random.choice(images)
    caption = generate_caption("image")

    print(f"📸 Image Post: {image_url}")
    media = create_media({"image_url": image_url, "caption": caption, "access_token": ACCESS_TOKEN})
    if "id" in media:
        result = publish_media(media["id"])
        print("✅ Image posted:", result)
    else:
        print("❌ Failed to create image media:", media)

def post_video():
    videos = fetch_videos()
    if not videos:
        print("❌ No videos found")
        return
    video_url = random.choice(videos)
    caption = generate_caption("video")

    print(f"🎥 Processing Video with Music & Subtitles: {video_url}")

    # Process video with music and subtitles
    processed_video_path = "processed_video.mp4"
    if add_music_and_subtitles(video_url, caption, processed_video_path):
        print("✅ Video processed successfully with music and subtitles")

        # Upload processed video file
        result = upload_video_file(processed_video_path, caption)
        if "error" not in result:
            print("✅ Video posted successfully:", result)
        else:
            print("❌ Failed to post video:", result)

        # Cleanup processed file
        if os.path.exists(processed_video_path):
            os.remove(processed_video_path)
    else:
        print("❌ Failed to process video, posting original instead...")
        # Fallback to original video URL
        media = create_media({
            "video_url": video_url,
            "caption": caption,
            "media_type": "REELS",
            "access_token": ACCESS_TOKEN
        })

        if "id" in media:
            print(f"📤 Media container created: {media['id']}")
            result = publish_media(media["id"])
            if "error" not in result:
                print("✅ Original video posted successfully:", result)
            else:
                print("❌ Failed to publish video:", result)
        else:
            print("❌ Failed to create video media:", media)

def upload_video_file(video_path, caption):
    """
    Upload processed video file to a hosting service, then post to Instagram
    """
    try:
        print("📤 Uploading processed video to hosting service...")

        # Upload to file.io (free temporary hosting)
        with open(video_path, 'rb') as f:
            files = {'file': f}
            response = requests.post('https://file.io', files=files)

        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                hosted_url = result.get('link')
                print(f"✅ Video uploaded to: {hosted_url}")

                # Now use the hosted URL with Instagram API
                media = create_media({
                    "video_url": hosted_url,
                    "caption": caption,
                    "media_type": "REELS",
                    "access_token": ACCESS_TOKEN
                })

                if "id" in media:
                    print(f"📤 Media container created: {media['id']}")
                    print("⏳ Waiting for video to process before publishing...")
                    result = publish_media(media["id"])
                    return result
                else:
                    return {"error": {"message": f"Failed to create media container: {media}"}}
            else:
                return {"error": {"message": f"File upload failed: {result}"}}
        else:
            return {"error": {"message": f"Upload service error: {response.status_code}"}}

    except Exception as e:
        return {"error": {"message": str(e)}}
        
        return result
        
    except Exception as e:
        print(f"❌ Error uploading video: {e}")
        return {"error": {"message": str(e)}}

# Alternative simpler approach - skip processing for now and just add subtitles overlay
def post_video_simple():
    """
    Simplified video posting without complex processing
    """
    videos = fetch_videos()
    if not videos: 
        print("❌ No videos found")
        return
    video_url = random.choice(videos)
    caption = generate_caption("video")

    print(f"🎥 Video Post: {video_url}")
    
    # Use original video URL directly (no processing)
    media = create_media({
        "video_url": video_url, 
        "caption": caption, 
        "media_type": "REELS",
        "access_token": ACCESS_TOKEN
    })
    
    if "id" in media:
        print(f"📤 Media container created: {media['id']}")
        print("⏳ Waiting for video to process before publishing...")
        result = publish_media(media["id"])
        if "error" not in result:
            print("✅ Video posted successfully:", result)
        else:
            print("❌ Failed to publish video:", result)
    else:
        print("❌ Failed to create video media:", media)

def post_carousel():
    images = fetch_images(per_page=5)
    if len(images) < 2: 
        print("❌ Not enough images for carousel")
        return
    caption = generate_caption("carousel")
    selected = random.sample(images, min(3, len(images)))  # 3 images per carousel (or less if not enough)

    container_ids = []
    for img in selected:
        media = create_media({"image_url": img, "is_carousel_item": True, "access_token": ACCESS_TOKEN})
        if "id" in media:
            container_ids.append(media["id"])
        else:
            print("❌ Failed to create carousel item:", media)

    if container_ids:
        payload = {
            "children": ",".join(container_ids),
            "caption": caption,
            "media_type": "CAROUSEL",
            "access_token": ACCESS_TOKEN
        }
        result = create_media(payload)
        if "id" in result:
            publish = publish_media(result["id"])
            print("✅ Carousel posted:", publish)
        else:
            print("❌ Failed to create carousel container:", result)

# ================= MAIN =================
if __name__ == "__main__":
    post_type = random.choice(["image", "video", "carousel"])
    print("Selected post type:", post_type)

    if post_type == "image":
        post_image()
    elif post_type == "video":
        post_video()  # Use full processing with music and subtitles
    else:
        post_carousel()