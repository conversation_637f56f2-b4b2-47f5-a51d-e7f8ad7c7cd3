import os
import random
import requests
import json
import google.generativeai as genai

# --- Setup ---
ACCESS_TOKEN = os.getenv("EAAVh81wZCRlEBPYHKPaC34VnBWWRbGWzAjaSWDDwvgciTZClG2TJkl8dFihV7AgwJZCYrh2l0Nzc70iEF8GHZC26vrvPKczdErxOcd7e0gdP4wzgAXi6fHm8MkULj22UiKszS8iwk8PK3DiXQ99tRI2dDkSZBuXwd9OHdJqlmm0ZCU6Qn1hZANNXQxsnrVXnKMbNRLPSbQmdhAy")
USER_ID = os.getenv("17841470883624148")
PEXELS_API_KEY = os.getenv("Y42fCOJyjmNn88IP6nGhBmidAX2J8EiyMpkk7vse4IjB3YsILyAoBEhw")
GEMINI_API_KEY = os.getenv("GEMINI_API_KEY")

genai.configure(api_key=GEMINI_API_KEY)


# --- Gemini Caption Generator ---
def generate_caption(post_type="image"):
    try:
        prompt = f"""
        Generate a short Instagram {post_type} caption and 5-7 unique hashtags.
        Return JSON only in this format:
        {{
            "caption": "your caption here",
            "hashtags": "#tag1 #tag2 #tag3 ..."
        }}
        """

        response = genai.GenerativeModel("gemini-1.5-flash").generate_content(prompt)

        text = response.text.strip()
        data = json.loads(text)

        caption = data.get("caption", "✨ A beautiful moment captured!")
        hashtags = data.get("hashtags", "#life #love #vibes")

        return caption, hashtags

    except Exception as e:
        print("❌ Error generating caption:", e)
        return "✨ Default caption", "#life #love #vibes"


# --- Fetch Unique Media from Pexels ---
def fetch_unique_image():
    headers = {"Authorization": PEXELS_API_KEY}
    query = random.choice(["nature", "city", "travel", "art", "technology", "food", "abstract"])
    url = f"https://api.pexels.com/v1/search?query={query}&per_page=15&page={random.randint(1,50)}"
    r = requests.get(url, headers=headers).json()
    if "photos" in r and r["photos"]:
        return random.choice(r["photos"])["src"]["large2x"]
    return None


def fetch_unique_video():
    headers = {"Authorization": PEXELS_API_KEY}
    query = random.choice(["nature", "city", "ocean", "wildlife", "sports", "technology"])
    url = f"https://api.pexels.com/videos/search?query={query}&per_page=15&page={random.randint(1,50)}"
    r = requests.get(url, headers=headers).json()
    if "videos" in r and r["videos"]:
        return random.choice(r["videos"])["video_files"][0]["link"]
    return None


# --- Instagram API Helpers ---
def create_container(media_url, caption, media_type="IMAGE", is_carousel=False):
    url = f"https://graph.facebook.com/v18.0/{USER_ID}/media"
    payload = {
        "caption": caption,
        "access_token": ACCESS_TOKEN
    }
    if media_type == "IMAGE":
        payload["image_url"] = media_url
    elif media_type == "VIDEO":
        payload["video_url"] = media_url
    elif media_type == "CAROUSEL" and is_carousel:
        payload["media_type"] = "CAROUSEL"
        payload["children"] = media_url

    res = requests.post(url, data=payload).json()
    return res


def publish_container(container_id):
    url = f"https://graph.facebook.com/v18.0/{USER_ID}/media_publish"
    payload = {
        "creation_id": container_id,
        "access_token": ACCESS_TOKEN
    }
    res = requests.post(url, data=payload).json()
    return res


# --- Post Functions ---
def post_image():
    img_url = fetch_unique_image()
    if not img_url:
        print("❌ No image found")
        return
    caption, hashtags = generate_caption("image")
    final_caption = f"{caption}\n\n{hashtags}"
    print("📸 Posting Image:", img_url)
    container = create_container(img_url, final_caption, "IMAGE")
    if "id" in container:
        publish = publish_container(container["id"])
        print("✅ Image posted:", publish)
    else:
        print("❌ Error:", container)


def post_video():
    vid_url = fetch_unique_video()
    if not vid_url:
        print("❌ No video found")
        return
    caption, hashtags = generate_caption("video")
    final_caption = f"{caption}\n\n{hashtags}"
    print("🎥 Posting Video:", vid_url)
    container = create_container(vid_url, final_caption, "VIDEO")
    if "id" in container:
        publish = publish_container(container["id"])
        print("✅ Video posted:", publish)
    else:
        print("❌ Error:", container)


def post_carousel():
    print("📚 Posting Carousel")
    urls = [fetch_unique_image() for _ in range(3)]
    urls = [u for u in urls if u]

    if not urls:
        print("❌ No images for carousel")
        return

    child_ids = []
    for u in urls:
        res = create_container(u, "", "IMAGE")
        if "id" in res:
            child_ids.append(res["id"])

    if not child_ids:
        print("❌ No child containers")
        return

    caption, hashtags = generate_caption("carousel")
    final_caption = f"{caption}\n\n{hashtags}"

    url = f"https://graph.facebook.com/v18.0/{USER_ID}/media"
    payload = {
        "caption": final_caption,
        "access_token": ACCESS_TOKEN,
        "children": ",".join(child_ids),
        "media_type": "CAROUSEL"
    }
    res = requests.post(url, data=payload).json()

    if "id" in res:
        publish = publish_container(res["id"])
        print("✅ Carousel posted:", publish)
    else:
        print("❌ Error:", res)


# --- Main Execution ---
if __name__ == "__main__":
    choice = random.choice(["image", "video", "carousel"])
    print(f"\nSelected post type: {choice}")

    if choice == "image":
        post_image()
    elif choice == "video":
        post_video()
    else:
        post_carousel()
